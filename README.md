# Kids Tutorial App 📚

A comprehensive educational mobile app for kids built with React Native and Expo, covering English, Math, Hindi, and Indian National Anthem.

## Features 🌟

### English Learning 🔤
- **ABC Learning**: Interactive alphabet with A-Z letters, each with associated words and emojis
- **Nursery Rhymes**: Collection of popular nursery rhymes including:
  - Twinkle Twinkle Little Star
  - Humpty Dumpty
  - Mary Had a Little Lamb
  - Baa Baa Black Sheep
  - Jack and Jill
  - Row Row Row Your Boat

### Math Learning 🔢
- **Numbers 1-10**: Basic counting for beginners
- **Numbers 1-50**: Extended counting practice
- **Numbers 10-100**: Learning tens (10, 20, 30, etc.)
- Interactive number cards with pronunciations

### Hindi Learning 🇮🇳
- **Hindi Alphabet**: Complete Devanagari script (क, ख, ग, घ...)
- Each letter includes:
  - Proper pronunciation
  - Example words
  - English meanings

### National Anthem 🎵
- **Jana Gana Mana**: Complete Indian National Anthem
- Features:
  - Hindi text with Devanagari script
  - Pronunciation guide
  - English meaning
  - Historical information
  - Interactive play/pause functionality

## App Structure 📱

```
src/
├── screens/
│   ├── HomeScreen.js          # Main menu
│   ├── EnglishScreen.js       # English learning options
│   ├── AlphabetScreen.js      # ABC learning
│   ├── RhymesScreen.js        # Nursery rhymes
│   ├── MathScreen.js          # Math learning options
│   ├── NumbersScreen.js       # Number learning
│   ├── HindiScreen.js         # Hindi learning options
│   ├── HindiAlphabetScreen.js # Hindi alphabet
│   └── NationalAnthemScreen.js # National anthem
```

## Installation & Setup 🚀

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn
- Expo CLI
- Expo Go app on your mobile device

### Steps

1. **Clone/Download the project**
   ```bash
   cd mobileApp
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npx expo start
   ```

4. **Run on device**
   - Install Expo Go app on your phone
   - Scan the QR code displayed in terminal
   - Or use `npx expo start --android` / `npx expo start --ios`

## Dependencies 📦

- **expo**: ~53.0.19
- **react**: 18.2.0
- **react-native**: ^0.72.17
- **@react-navigation/native**: ^6.1.9
- **@react-navigation/stack**: ^6.3.20
- **react-native-screens**: ~3.22.0
- **react-native-safe-area-context**: 4.6.3
- **expo-av**: ~13.4.1 (for future audio features)
- **expo-font**: ~11.4.0 (for custom fonts)

## Features in Detail 📋

### User Interface
- **Kid-friendly design**: Large buttons, bright colors, engaging visuals
- **Touch interactions**: Tap to learn, interactive cards
- **Responsive layout**: Works on different screen sizes
- **Smooth navigation**: Easy-to-use navigation between sections

### Educational Content
- **Progressive learning**: From basic to advanced concepts
- **Visual learning**: Emojis and visual cues for better understanding
- **Interactive feedback**: Alerts and responses for user interactions
- **Cultural education**: Indian National Anthem with cultural context

## Troubleshooting 🔧

### Common Issues

1. **Metro bundler issues**
   ```bash
   npx expo start --clear
   ```

2. **Dependency conflicts**
   ```bash
   rm -rf node_modules
   npm install
   ```

3. **Expo CLI not found**
   ```bash
   npm install -g @expo/cli
   ```

## Future Enhancements 🚀

- [ ] Audio pronunciation for all letters and numbers
- [ ] Interactive games and quizzes
- [ ] Progress tracking
- [ ] Offline mode
- [ ] More languages support
- [ ] Animated characters
- [ ] Voice recognition
- [ ] Parent dashboard

## Contributing 🤝

This is an educational project. Feel free to:
- Add more educational content
- Improve UI/UX
- Add audio features
- Implement games and quizzes
- Add more languages

## License 📄

This project is created for educational purposes.

---

**Made with ❤️ for Kids' Education**
